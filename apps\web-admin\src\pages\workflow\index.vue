<template>
  <div class="workflow-page">
    <!-- 页面头部 -->
    <div class="mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-semibold text-gray-900">工作流</h1>
          <p class="mt-1 text-sm text-gray-500">创建和管理您的AI工作流</p>
        </div>
        <div class="flex items-center space-x-3">
          <button
            @click="showCreateModal = true"
            class="btn btn-primary"
          >
            <PlusIcon class="w-4 h-4 mr-2" />
            创建工作流
          </button>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="mb-6">
      <div class="flex items-center space-x-4">
        <div class="flex-1 max-w-md">
          <div class="relative">
            <MagnifyingGlassIcon class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              v-model="searchQuery"
              type="text"
              placeholder="搜索工作流..."
              class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>
        <select
          v-model="filterMode"
          class="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="">所有类型</option>
          <option value="workflow">工作流</option>
          <option value="chatflow">聊天流</option>
        </select>
      </div>
    </div>

    <!-- 工作流列表 -->
    <div v-if="loading" class="flex items-center justify-center py-12">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    </div>

    <div v-else-if="error" class="text-center py-12">
      <div class="text-red-600 mb-2">{{ error }}</div>
      <button @click="fetchWorkflows" class="btn btn-secondary">
        重试
      </button>
    </div>

    <div v-else-if="filteredWorkflows.length === 0" class="text-center py-12">
      <div class="text-gray-500 mb-4">
        {{ searchQuery ? '未找到匹配的工作流' : '暂无工作流' }}
      </div>
      <button
        v-if="!searchQuery"
        @click="showCreateModal = true"
        class="btn btn-primary"
      >
        创建第一个工作流
      </button>
    </div>

    <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div
        v-for="workflow in filteredWorkflows"
        :key="workflow.id"
        class="workflow-card bg-white rounded-lg border border-gray-200 hover:border-gray-300 hover:shadow-md transition-all duration-200 cursor-pointer"
        @click="openWorkflow(workflow.id)"
      >
        <div class="p-6">
          <!-- 工作流头部 -->
          <div class="flex items-start justify-between mb-4">
            <div class="flex items-center space-x-3">
              <div
                :class="[
                  'w-10 h-10 rounded-lg flex items-center justify-center',
                  workflow.mode === 'chatflow' ? 'bg-blue-100' : 'bg-green-100'
                ]"
              >
                <component
                  :is="workflow.mode === 'chatflow' ? ChatBubbleLeftRightIcon : ShareIcon"
                  :class="[
                    'w-5 h-5',
                    workflow.mode === 'chatflow' ? 'text-blue-600' : 'text-green-600'
                  ]"
                />
              </div>
              <div class="flex-1 min-w-0">
                <h3 class="text-lg font-medium text-gray-900 truncate">
                  {{ workflow.name }}
                </h3>
                <p class="text-sm text-gray-500">
                  {{ workflow.mode === 'chatflow' ? '聊天流' : '工作流' }}
                </p>
              </div>
            </div>

            <!-- 操作菜单 -->
            <div class="relative" @click.stop>
              <button
                @click="toggleMenu(workflow.id)"
                class="p-1 hover:bg-gray-100 rounded"
              >
                <EllipsisVerticalIcon class="w-4 h-4 text-gray-500" />
              </button>

              <div
                v-if="activeMenu === workflow.id"
                class="absolute right-0 top-8 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10"
              >
                <div class="py-1">
                  <button
                    @click="editWorkflow(workflow.id)"
                    class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <PencilIcon class="w-4 h-4 inline mr-2" />
                    编辑
                  </button>
                  <button
                    @click="duplicateWorkflow(workflow.id)"
                    class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <DocumentDuplicateIcon class="w-4 h-4 inline mr-2" />
                    复制
                  </button>
                  <hr class="my-1" />
                  <button
                    @click="deleteWorkflow(workflow.id)"
                    class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                  >
                    <TrashIcon class="w-4 h-4 inline mr-2" />
                    删除
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 工作流描述 -->
          <p v-if="workflow.description" class="text-sm text-gray-600 mb-4 line-clamp-2">
            {{ workflow.description }}
          </p>

          <!-- 工作流统计 -->
          <div class="flex items-center justify-between text-sm text-gray-500">
            <div class="flex items-center space-x-4">
              <span>{{ workflow.graph.nodes.length }} 个节点</span>
              <span>{{ workflow.graph.edges.length }} 个连接</span>
            </div>
            <span>{{ formatDate(workflow.created_at) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建工作流模态框 -->
    <div
      v-if="showCreateModal"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      @click="showCreateModal = false"
    >
      <div
        class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4"
        @click.stop
      >
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">创建新工作流</h3>
        </div>

        <form @submit.prevent="handleCreateWorkflow" class="px-6 py-4 space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              工作流名称
            </label>
            <input
              v-model="createForm.name"
              type="text"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="输入工作流名称"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              描述 (可选)
            </label>
            <textarea
              v-model="createForm.description"
              rows="3"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="输入工作流描述"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              类型
            </label>
            <div class="space-y-2">
              <label class="flex items-center">
                <input
                  v-model="createForm.mode"
                  type="radio"
                  value="workflow"
                  class="text-blue-600 focus:ring-blue-500"
                />
                <span class="ml-2 text-sm text-gray-700">工作流 - 适用于自动化和批处理场景</span>
              </label>
              <label class="flex items-center">
                <input
                  v-model="createForm.mode"
                  type="radio"
                  value="chatflow"
                  class="text-blue-600 focus:ring-blue-500"
                />
                <span class="ml-2 text-sm text-gray-700">聊天流 - 适用于对话和客服场景</span>
              </label>
            </div>
          </div>
        </form>

        <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
          <button
            @click="showCreateModal = false"
            type="button"
            class="btn btn-secondary"
          >
            取消
          </button>
          <button
            @click="handleCreateWorkflow"
            :disabled="!createForm.name.trim()"
            class="btn btn-primary"
          >
            创建
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  PlusIcon,
  MagnifyingGlassIcon,
  ChatBubbleLeftRightIcon,
  ShareIcon,
  EllipsisVerticalIcon,
  PencilIcon,
  DocumentDuplicateIcon,
  TrashIcon
} from '@heroicons/vue/24/outline'
import { useWorkflowStore } from '@/stores/workflow'
import type { WorkflowConfig } from '@/types/workflow'

const router = useRouter()
const workflowStore = useWorkflowStore()

// 响应式数据
const searchQuery = ref('')
const filterMode = ref('')
const showCreateModal = ref(false)
const activeMenu = ref<string | null>(null)

const createForm = ref({
  name: '',
  description: '',
  mode: 'workflow' as 'workflow' | 'chatflow'
})

// 计算属性
const workflows = computed(() => workflowStore.workflows)
const loading = computed(() => workflowStore.loading)
const error = computed(() => workflowStore.error)

const filteredWorkflows = computed(() => {
  let filtered = workflows.value

  // 按搜索关键词筛选
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(workflow =>
      workflow.name.toLowerCase().includes(query) ||
      workflow.description?.toLowerCase().includes(query)
    )
  }

  // 按类型筛选
  if (filterMode.value) {
    filtered = filtered.filter(workflow => workflow.mode === filterMode.value)
  }

  return filtered
})

// 方法
const fetchWorkflows = async () => {
  await workflowStore.fetchWorkflows()
}

const openWorkflow = (workflowId: string) => {
  router.push(`/workflow/${workflowId}/editor`)
}

const editWorkflow = (workflowId: string) => {
  activeMenu.value = null
  router.push(`/workflow/${workflowId}/editor`)
}

const duplicateWorkflow = async (workflowId: string) => {
  activeMenu.value = null
  try {
    const originalWorkflow = workflows.value.find(w => w.id === workflowId)
    if (originalWorkflow) {
      await workflowStore.createWorkflow({
        name: `${originalWorkflow.name} (副本)`,
        description: originalWorkflow.description,
        mode: originalWorkflow.mode,
        graph: originalWorkflow.graph,
        variables: originalWorkflow.variables,
        features: originalWorkflow.features
      })
    }
  } catch (error) {
    console.error('复制工作流失败:', error)
  }
}

const deleteWorkflow = async (workflowId: string) => {
  activeMenu.value = null
  if (confirm('确定要删除这个工作流吗？此操作不可撤销。')) {
    try {
      await workflowStore.deleteWorkflow(workflowId)
    } catch (error) {
      console.error('删除工作流失败:', error)
    }
  }
}

const handleCreateWorkflow = async () => {
  if (!createForm.value.name.trim()) return

  try {
    const newWorkflow = await workflowStore.createWorkflow({
      name: createForm.value.name.trim(),
      description: createForm.value.description.trim() || undefined,
      mode: createForm.value.mode
    })

    showCreateModal.value = false
    createForm.value = {
      name: '',
      description: '',
      mode: 'workflow'
    }

    // 跳转到编辑器
    router.push(`/workflow/${newWorkflow.id}/editor`)
  } catch (error) {
    console.error('创建工作流失败:', error)
  }
}

const toggleMenu = (workflowId: string) => {
  activeMenu.value = activeMenu.value === workflowId ? null : workflowId
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffTime = Math.abs(now.getTime() - date.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays === 1) {
    return '今天'
  } else if (diffDays === 2) {
    return '昨天'
  } else if (diffDays <= 7) {
    return `${diffDays} 天前`
  } else {
    return date.toLocaleDateString('zh-CN')
  }
}

// 点击外部关闭菜单
const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement
  if (!target.closest('.relative')) {
    activeMenu.value = null
  }
}

// 生命周期
onMounted(async () => {
  await fetchWorkflows()
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.workflow-card {
  transition: all 0.2s ease-in-out;
}

.workflow-card:hover {
  transform: translateY(-2px);
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
