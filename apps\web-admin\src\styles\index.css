@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    @apply text-gray-900 bg-gray-50;
  }

  /* 滚动条样式 */
  ::-webkit-scrollbar {
    @apply w-2 h-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }
}

@layer components {
  /* 按钮组件样式 */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }

  .btn-secondary {
    @apply btn bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
  }

  .btn-outline {
    @apply btn border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500;
  }

  .btn-danger {
    @apply btn bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
  }

  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }

  .btn-lg {
    @apply px-6 py-3 text-base;
  }

  /* 输入框组件样式 */
  .input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500;
  }

  .input-error {
    @apply border-red-300 focus:ring-red-500 focus:border-red-500;
  }

  /* 卡片组件样式 */
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200;
  }

  .card-header {
    @apply px-6 py-4 border-b border-gray-200;
  }

  .card-body {
    @apply px-6 py-4;
  }

  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
  }

  /* 导航样式 */
  .nav-link {
    @apply flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200;
  }

  .nav-link-active {
    @apply bg-primary-100 text-primary-700;
  }

  .nav-link-inactive {
    @apply text-gray-600 hover:bg-gray-100 hover:text-gray-900;
  }

  /* 表格样式 */
  .table {
    @apply min-w-full divide-y divide-gray-200;
  }

  .table-header {
    @apply bg-gray-50;
  }

  .table-header-cell {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
  }

  .table-body {
    @apply bg-white divide-y divide-gray-200;
  }

  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
  }

  /* 徽章样式 */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply bg-primary-100 text-primary-800;
  }

  .badge-success {
    @apply bg-green-100 text-green-800;
  }

  .badge-warning {
    @apply bg-yellow-100 text-yellow-800;
  }

  .badge-error {
    @apply bg-red-100 text-red-800;
  }

  .badge-gray {
    @apply bg-gray-100 text-gray-800;
  }
}

@layer utilities {
  /* 自定义工具类 */
  .text-balance {
    text-wrap: balance;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* 动画工具类 */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.6s ease-out;
  }

  .animate-slide-down {
    animation: slideDown 0.6s ease-out;
  }

  .animate-bounce-in {
    animation: bounceIn 0.8s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.4s ease-out;
  }

  /* 响应式工具类 */
  .container-fluid {
    @apply w-full max-w-none px-4 sm:px-6 lg:px-8;
  }

  .container-narrow {
    @apply max-w-4xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* 状态工具类 */
  .loading {
    @apply opacity-50 pointer-events-none;
  }

  .disabled {
    @apply opacity-50 cursor-not-allowed;
  }
}

/* 第三方库样式覆盖 */
.vue-flow__node {
  @apply border border-gray-200 rounded-lg shadow-sm;
}

.vue-flow__edge {
  @apply stroke-gray-400;
}

.vue-flow__edge.selected {
  @apply stroke-primary-500;
}

/* Monaco Editor 主题适配 */
.monaco-editor {
  @apply rounded-md border border-gray-200;
}

/* Dashboard 特定样式 */
.dashboard {
  @apply w-full;
}

.dashboard .card {
  @apply h-full; /* 确保卡片高度一致 */
}

/* 响应式断点 */
@media (max-width: 640px) {
  .card {
    @apply rounded-none border-x-0;
  }

  .card-header,
  .card-body,
  .card-footer {
    @apply px-4;
  }

  .dashboard {
    @apply px-0; /* 移动端移除额外边距 */
  }
}

/* 确保网格项目高度一致 */
@media (min-width: 1024px) {
  .dashboard .grid > .card {
    @apply flex flex-col;
  }

  .dashboard .card-body {
    @apply flex-1;
  }
}


