<template>
  <BaseNode
    :data="data"
    :selected="selected"
    :disabled="disabled"
    :status="status"
    @click="$emit('click', $event)"
    @double-click="$emit('doubleClick', $event)"
  >
    <template #content>
      <div class="space-y-2">
        <p class="text-xs text-gray-500">开始节点</p>
        <div v-if="data.variables.length > 0" class="space-y-1">
          <p class="text-xs font-medium text-gray-700">输入变量:</p>
          <div class="space-y-1">
            <div 
              v-for="variable in data.variables.slice(0, 3)" 
              :key="variable.variable"
              class="flex items-center text-xs text-gray-600"
            >
              <span class="w-2 h-2 bg-blue-400 rounded-full mr-2"></span>
              <span class="truncate">{{ variable.label || variable.variable }}</span>
              <span v-if="variable.required" class="text-red-500 ml-1">*</span>
            </div>
            <div v-if="data.variables.length > 3" class="text-xs text-gray-400">
              +{{ data.variables.length - 3 }} 更多...
            </div>
          </div>
        </div>
        <div v-else class="text-xs text-gray-400">
          暂无输入变量
        </div>
      </div>
    </template>
  </BaseNode>
</template>

<script setup lang="ts">
import BaseNode from './BaseNode.vue'
import type { StartNodeData } from '@/types/workflow'

interface Props {
  data: StartNodeData
  selected?: boolean
  disabled?: boolean
  status?: 'running' | 'success' | 'error' | 'idle'
}

interface Emits {
  (e: 'click', event: MouseEvent): void
  (e: 'doubleClick', event: MouseEvent): void
}

defineProps<Props>()
defineEmits<Emits>()
</script>
