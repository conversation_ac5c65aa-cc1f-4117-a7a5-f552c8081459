<template>
  <div class="node-properties-panel h-full flex flex-col bg-white">
    <!-- 面板标题 -->
    <div class="flex-shrink-0 px-4 py-3 border-b border-gray-200">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-2">
          <component 
            :is="nodeIcon" 
            class="w-5 h-5 text-gray-600"
          />
          <h3 class="text-sm font-medium text-gray-900">
            {{ nodeConfig?.label || '节点属性' }}
          </h3>
        </div>
        <button
          @click="$emit('close')"
          class="p-1 hover:bg-gray-100 rounded"
        >
          <XMarkIcon class="w-4 h-4 text-gray-500" />
        </button>
      </div>
    </div>
    
    <!-- 属性表单 -->
    <div class="flex-1 overflow-y-auto">
      <div class="p-4 space-y-6">
        <!-- 基础信息 -->
        <div class="space-y-4">
          <h4 class="text-sm font-medium text-gray-900">基础信息</h4>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              节点标题
            </label>
            <input
              v-model="localData.title"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="输入节点标题"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              节点描述
            </label>
            <textarea
              v-model="localData.desc"
              rows="2"
              class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="输入节点描述"
            />
          </div>
        </div>
        
        <!-- 节点特定配置 -->
        <div v-if="node.type === 'start'" class="space-y-4">
          <h4 class="text-sm font-medium text-gray-900">输入变量</h4>
          
          <div class="space-y-3">
            <div
              v-for="(variable, index) in startNodeData.variables"
              :key="index"
              class="p-3 border border-gray-200 rounded-lg space-y-3"
            >
              <div class="flex items-center justify-between">
                <span class="text-sm font-medium text-gray-700">变量 {{ index + 1 }}</span>
                <button
                  @click="removeVariable(index)"
                  class="text-red-500 hover:text-red-700"
                >
                  <TrashIcon class="w-4 h-4" />
                </button>
              </div>
              
              <div class="grid grid-cols-2 gap-3">
                <div>
                  <label class="block text-xs font-medium text-gray-700 mb-1">
                    变量名
                  </label>
                  <input
                    v-model="variable.variable"
                    type="text"
                    class="w-full px-2 py-1 border border-gray-300 rounded text-xs"
                    placeholder="variable_name"
                  />
                </div>
                <div>
                  <label class="block text-xs font-medium text-gray-700 mb-1">
                    类型
                  </label>
                  <select
                    v-model="variable.type"
                    class="w-full px-2 py-1 border border-gray-300 rounded text-xs"
                  >
                    <option value="text">文本</option>
                    <option value="paragraph">段落</option>
                    <option value="select">选择</option>
                    <option value="number">数字</option>
                    <option value="file">文件</option>
                    <option value="file-list">文件列表</option>
                  </select>
                </div>
              </div>
              
              <div>
                <label class="block text-xs font-medium text-gray-700 mb-1">
                  显示标签
                </label>
                <input
                  v-model="variable.label"
                  type="text"
                  class="w-full px-2 py-1 border border-gray-300 rounded text-xs"
                  placeholder="输入显示标签"
                />
              </div>
              
              <div class="flex items-center space-x-4">
                <label class="flex items-center">
                  <input
                    v-model="variable.required"
                    type="checkbox"
                    class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span class="ml-2 text-xs text-gray-700">必填</span>
                </label>
                
                <div v-if="variable.type === 'text'" class="flex-1">
                  <label class="block text-xs font-medium text-gray-700 mb-1">
                    最大长度
                  </label>
                  <input
                    v-model.number="variable.max_length"
                    type="number"
                    class="w-full px-2 py-1 border border-gray-300 rounded text-xs"
                    placeholder="256"
                  />
                </div>
              </div>
              
              <div v-if="variable.type === 'select'">
                <label class="block text-xs font-medium text-gray-700 mb-1">
                  选项 (每行一个)
                </label>
                <textarea
                  :value="variable.options?.join('\n') || ''"
                  @input="updateVariableOptions(index, $event)"
                  rows="3"
                  class="w-full px-2 py-1 border border-gray-300 rounded text-xs"
                  placeholder="选项1&#10;选项2&#10;选项3"
                />
              </div>
            </div>
            
            <button
              @click="addVariable"
              class="w-full py-2 border-2 border-dashed border-gray-300 rounded-lg text-sm text-gray-500 hover:border-gray-400 hover:text-gray-600"
            >
              + 添加变量
            </button>
          </div>
        </div>
        
        <!-- LLM 节点配置 -->
        <div v-else-if="node.type === 'llm'" class="space-y-4">
          <h4 class="text-sm font-medium text-gray-900">模型配置</h4>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              模型提供商
            </label>
            <select
              v-model="llmNodeData.model.provider"
              class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">选择提供商</option>
              <option value="openai">OpenAI</option>
              <option value="anthropic">Anthropic</option>
              <option value="azure_openai">Azure OpenAI</option>
              <option value="zhipuai">智谱AI</option>
              <option value="tongyi">通义千问</option>
            </select>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              模型名称
            </label>
            <input
              v-model="llmNodeData.model.name"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="gpt-3.5-turbo"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              提示词模板
            </label>
            <div class="space-y-2">
              <div
                v-for="(prompt, index) in llmNodeData.prompt_template"
                :key="index"
                class="flex space-x-2"
              >
                <select
                  v-model="prompt.role"
                  class="flex-shrink-0 px-2 py-1 border border-gray-300 rounded text-sm"
                >
                  <option value="system">System</option>
                  <option value="user">User</option>
                  <option value="assistant">Assistant</option>
                </select>
                <textarea
                  v-model="prompt.text"
                  rows="2"
                  class="flex-1 px-2 py-1 border border-gray-300 rounded text-sm"
                  :placeholder="`输入${prompt.role}提示词...`"
                />
                <button
                  @click="removePrompt(index)"
                  class="flex-shrink-0 p-1 text-red-500 hover:text-red-700"
                >
                  <TrashIcon class="w-4 h-4" />
                </button>
              </div>
              <button
                @click="addPrompt"
                class="w-full py-2 border-2 border-dashed border-gray-300 rounded text-sm text-gray-500 hover:border-gray-400"
              >
                + 添加提示词
              </button>
            </div>
          </div>
          
          <!-- 功能开关 -->
          <div class="space-y-3">
            <h5 class="text-sm font-medium text-gray-700">功能配置</h5>
            
            <label class="flex items-center">
              <input
                v-model="llmNodeData.memory.window.enabled"
                type="checkbox"
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span class="ml-2 text-sm text-gray-700">启用对话记忆</span>
            </label>
            
            <div v-if="llmNodeData.memory.window.enabled" class="ml-6">
              <label class="block text-xs font-medium text-gray-700 mb-1">
                记忆窗口大小
              </label>
              <input
                v-model.number="llmNodeData.memory.window.size"
                type="number"
                class="w-20 px-2 py-1 border border-gray-300 rounded text-xs"
                min="1"
                max="50"
              />
            </div>
            
            <label class="flex items-center">
              <input
                v-model="llmNodeData.context.enabled"
                type="checkbox"
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span class="ml-2 text-sm text-gray-700">启用上下文</span>
            </label>
            
            <label class="flex items-center">
              <input
                v-model="llmNodeData.vision.enabled"
                type="checkbox"
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span class="ml-2 text-sm text-gray-700">启用视觉能力</span>
            </label>
          </div>
        </div>
        
        <!-- 其他节点类型的配置... -->
        <div v-else class="text-sm text-gray-500">
          该节点类型的配置面板正在开发中...
        </div>
      </div>
    </div>
    
    <!-- 操作按钮 -->
    <div class="flex-shrink-0 p-4 border-t border-gray-200">
      <div class="flex space-x-2">
        <button
          @click="handleSave"
          class="flex-1 btn btn-primary btn-sm"
        >
          保存
        </button>
        <button
          @click="handleReset"
          class="flex-1 btn btn-secondary btn-sm"
        >
          重置
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { XMarkIcon, TrashIcon } from '@heroicons/vue/24/outline'
import { getNodeConfig } from './nodes'
import type { WorkflowNode, StartNodeData, LLMNodeData } from '@/types/workflow'

interface Props {
  node: WorkflowNode
}

interface Emits {
  (e: 'update', nodeId: string, data: any): void
  (e: 'close'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 本地数据副本
const localData = ref({ ...props.node.data })

// 计算属性
const nodeConfig = computed(() => getNodeConfig(props.node.type))
const nodeIcon = computed(() => {
  // 这里应该返回对应的图标组件
  return 'div' // 临时占位
})

// 类型特定的数据
const startNodeData = computed(() => {
  if (props.node.type === 'start') {
    return localData.value as StartNodeData
  }
  return { variables: [] } as StartNodeData
})

const llmNodeData = computed(() => {
  if (props.node.type === 'llm') {
    return localData.value as LLMNodeData
  }
  return {
    model: { provider: '', name: '', mode: '', completion_params: {} },
    prompt_template: [],
    memory: { window: { enabled: false, size: 10 } },
    context: { enabled: false },
    vision: { enabled: false }
  } as LLMNodeData
})

// Start 节点方法
const addVariable = () => {
  if (props.node.type === 'start') {
    const data = localData.value as StartNodeData
    data.variables.push({
      variable: `var_${data.variables.length + 1}`,
      type: 'text',
      label: `变量 ${data.variables.length + 1}`,
      required: false
    })
  }
}

const removeVariable = (index: number) => {
  if (props.node.type === 'start') {
    const data = localData.value as StartNodeData
    data.variables.splice(index, 1)
  }
}

const updateVariableOptions = (index: number, event: Event) => {
  if (props.node.type === 'start') {
    const target = event.target as HTMLTextAreaElement
    const data = localData.value as StartNodeData
    data.variables[index].options = target.value.split('\n').filter(option => option.trim())
  }
}

// LLM 节点方法
const addPrompt = () => {
  if (props.node.type === 'llm') {
    const data = localData.value as LLMNodeData
    data.prompt_template.push({
      role: 'user',
      text: ''
    })
  }
}

const removePrompt = (index: number) => {
  if (props.node.type === 'llm') {
    const data = localData.value as LLMNodeData
    data.prompt_template.splice(index, 1)
  }
}

// 操作方法
const handleSave = () => {
  emit('update', props.node.id, localData.value)
}

const handleReset = () => {
  localData.value = { ...props.node.data }
}

// 监听节点变化
watch(
  () => props.node,
  (newNode) => {
    localData.value = { ...newNode.data }
  },
  { deep: true }
)
</script>

<style scoped>
.node-properties-panel {
  min-width: 320px;
}
</style>
