/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    BaseNode: typeof import('./src/components/workflow/nodes/BaseNode.vue')['default']
    Breadcrumb: typeof import('./src/components/layout/Breadcrumb.vue')['default']
    EndNode: typeof import('./src/components/workflow/nodes/EndNode.vue')['default']
    Header: typeof import('./src/components/layout/Header.vue')['default']
    LLMNode: typeof import('./src/components/workflow/nodes/LLMNode.vue')['default']
    NodePanel: typeof import('./src/components/workflow/NodePanel.vue')['default']
    NodePropertiesPanel: typeof import('./src/components/workflow/NodePropertiesPanel.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Sidebar: typeof import('./src/components/layout/Sidebar.vue')['default']
    StartNode: typeof import('./src/components/workflow/nodes/StartNode.vue')['default']
    WorkflowEditor: typeof import('./src/components/workflow/WorkflowEditor.vue')['default']
  }
}
