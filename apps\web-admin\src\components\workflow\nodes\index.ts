/**
 * 工作流节点组件导出
 */

import BaseNode from './BaseNode.vue'
import StartNode from './StartNode.vue'
import LLMNode from './LLMNode.vue'
import EndNode from './EndNode.vue'
// 暂时注释掉未创建的组件，先创建基础组件
// import KnowledgeRetrievalNode from './KnowledgeRetrievalNode.vue'
// import QuestionClassifierNode from './QuestionClassifierNode.vue'
// import IfElseNode from './IfElseNode.vue'
// import CodeNode from './CodeNode.vue'
// import TemplateNode from './TemplateNode.vue'
// import HttpRequestNode from './HttpRequestNode.vue'
// import ToolsNode from './ToolsNode.vue'
// import AnswerNode from './AnswerNode.vue'

import type { NodeType } from '@/types/workflow'

// 节点组件映射 - 暂时只包含已创建的组件
export const nodeComponents = {
  start: StartNode,
  end: EndNode,
  llm: LLMNode,
  // 其他组件待创建后添加
  // 'knowledge-retrieval': KnowledgeRetrievalNode,
  // 'question-classifier': QuestionClassifierNode,
  // 'if-else': IfElseNode,
  // code: CodeNode,
  // template: TemplateNode,
  // 'http-request': HttpRequestNode,
  // tools: ToolsNode,
  // answer: AnswerNode
} as const

// 节点类型配置
export const nodeTypeConfigs = {
  start: {
    label: '开始',
    description: '工作流的起始节点',
    icon: 'PlayIcon',
    color: 'green',
    category: 'basic'
  },
  end: {
    label: '结束',
    description: '工作流的结束节点',
    icon: 'StopIcon',
    color: 'red',
    category: 'basic'
  },
  llm: {
    label: 'LLM',
    description: '大语言模型节点',
    icon: 'CpuChipIcon',
    color: 'blue',
    category: 'ai'
  },
  'knowledge-retrieval': {
    label: '知识检索',
    description: '从知识库检索相关信息',
    icon: 'MagnifyingGlassIcon',
    color: 'purple',
    category: 'ai'
  },
  'question-classifier': {
    label: '问题分类器',
    description: '对用户问题进行分类',
    icon: 'QuestionMarkCircleIcon',
    color: 'yellow',
    category: 'ai'
  },
  'if-else': {
    label: '条件判断',
    description: 'IF/ELSE 条件分支',
    icon: 'QuestionMarkCircleIcon',
    color: 'orange',
    category: 'logic'
  },
  code: {
    label: '代码执行',
    description: '执行 Python 或 JavaScript 代码',
    icon: 'CodeBracketIcon',
    color: 'gray',
    category: 'logic'
  },
  template: {
    label: '模板',
    description: '文本模板转换',
    icon: 'DocumentTextIcon',
    color: 'indigo',
    category: 'transform'
  },
  'http-request': {
    label: 'HTTP 请求',
    description: '发送 HTTP 请求',
    icon: 'GlobeAltIcon',
    color: 'teal',
    category: 'integration'
  },
  tools: {
    label: '工具',
    description: '调用外部工具',
    icon: 'WrenchScrewdriverIcon',
    color: 'pink',
    category: 'integration'
  },
  answer: {
    label: '回答',
    description: '返回最终答案',
    icon: 'ChatBubbleLeftRightIcon',
    color: 'cyan',
    category: 'basic'
  }
} as const

// 节点分类
export const nodeCategories = {
  basic: {
    label: '基础节点',
    nodes: ['start', 'end', 'answer']
  },
  ai: {
    label: 'AI 节点',
    nodes: ['llm', 'knowledge-retrieval', 'question-classifier']
  },
  logic: {
    label: '逻辑节点',
    nodes: ['if-else', 'code']
  },
  transform: {
    label: '转换节点',
    nodes: ['template']
  },
  integration: {
    label: '集成节点',
    nodes: ['http-request', 'tools']
  }
} as const

// 获取节点组件
export function getNodeComponent(type: NodeType) {
  return nodeComponents[type] || BaseNode
}

// 获取节点配置
export function getNodeConfig(type: NodeType) {
  return nodeTypeConfigs[type]
}

// 获取所有节点类型
export function getAllNodeTypes(): NodeType[] {
  return Object.keys(nodeTypeConfigs) as NodeType[]
}

// 按分类获取节点类型
export function getNodeTypesByCategory() {
  return nodeCategories
}

export {
  BaseNode,
  StartNode,
  EndNode,
  LLMNode
  // 其他组件待创建后导出
  // KnowledgeRetrievalNode,
  // QuestionClassifierNode,
  // IfElseNode,
  // CodeNode,
  // TemplateNode,
  // HttpRequestNode,
  // ToolsNode,
  // AnswerNode
}
