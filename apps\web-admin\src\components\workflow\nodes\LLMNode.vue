<template>
  <BaseNode
    :data="data"
    :selected="selected"
    :disabled="disabled"
    :status="status"
    @click="$emit('click', $event)"
    @double-click="$emit('doubleClick', $event)"
  >
    <template #content>
      <div class="space-y-2">
        <div class="flex items-center justify-between">
          <p class="text-xs font-medium text-gray-700">LLM 节点</p>
          <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
            {{ data.model?.name || '未配置' }}
          </span>
        </div>
        
        <div v-if="data.model?.provider" class="text-xs text-gray-600">
          <span class="font-medium">提供商:</span> {{ data.model.provider }}
        </div>
        
        <div v-if="data.prompt_template?.length" class="space-y-1">
          <p class="text-xs font-medium text-gray-700">提示词模板:</p>
          <div class="bg-gray-50 p-2 rounded text-xs">
            <div 
              v-for="(prompt, index) in data.prompt_template.slice(0, 2)" 
              :key="index"
              class="mb-1 last:mb-0"
            >
              <span class="font-medium text-blue-600">{{ prompt.role }}:</span>
              <span class="text-gray-600 ml-1">
                {{ prompt.text.substring(0, 50) }}{{ prompt.text.length > 50 ? '...' : '' }}
              </span>
            </div>
            <div v-if="data.prompt_template.length > 2" class="text-gray-400">
              +{{ data.prompt_template.length - 2 }} 更多...
            </div>
          </div>
        </div>
        
        <div class="flex flex-wrap gap-1">
          <span v-if="data.memory?.window?.enabled" class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">
            记忆
          </span>
          <span v-if="data.context?.enabled" class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
            上下文
          </span>
          <span v-if="data.vision?.enabled" class="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded">
            视觉
          </span>
        </div>
      </div>
    </template>
  </BaseNode>
</template>

<script setup lang="ts">
import BaseNode from './BaseNode.vue'
import type { LLMNodeData } from '@/types/workflow'

interface Props {
  data: LLMNodeData
  selected?: boolean
  disabled?: boolean
  status?: 'running' | 'success' | 'error' | 'idle'
}

interface Emits {
  (e: 'click', event: MouseEvent): void
  (e: 'doubleClick', event: MouseEvent): void
}

defineProps<Props>()
defineEmits<Emits>()
</script>
